import React, { useState } from "react";
import {
  useRealTimeDrivers,
  useRealTimeBuses,
  useRealTimeRoutes,
} from "../hooks/useRealTimeData";
import { createDriver, updateDriver, deleteDriver } from "../firebase/services";
import {
  DEFAULT_DRIVER,
  DRIVER_STATUS,
  STATUS_COLORS,
} from "../utils/constants";
import {
  User,
  Plus,
  Edit,
  Trash2,
  Phone,
  Mail,
  MapPin,
  Bus,
  Route,
  Link,
  Unlink,
} from "lucide-react";
import DriverBusAssignment from "./assignments/DriverBusAssignment";

const DriverManager = () => {
  const { drivers, loading: driversLoading } = useRealTimeDrivers();
  const { buses } = useRealTimeBuses();
  const { routes } = useRealTimeRoutes();
  const [showForm, setShowForm] = useState(false);
  const [editingDriver, setEditingDriver] = useState(null);
  const [formData, setFormData] = useState(DEFAULT_DRIVER);
  const [showAssignments, setShowAssignments] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingDriver) {
        await updateDriver(editingDriver.id, formData);
      } else {
        await createDriver(formData);
      }
      setShowForm(false);
      setEditingDriver(null);
      setFormData(DEFAULT_DRIVER);
    } catch (error) {
      console.error("Error saving driver:", error);
    }
  };

  const handleEdit = (driver) => {
    setEditingDriver(driver);
    setFormData(driver);
    setShowForm(true);
  };

  const handleDelete = async (driverId) => {
    if (window.confirm("Are you sure you want to delete this driver?")) {
      try {
        await deleteDriver(driverId);
      } catch (error) {
        console.error("Error deleting driver:", error);
      }
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    const processedValue =
      type === "number" ? (value === "" ? 0 : Number(value)) : value;

    if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setFormData((prev) => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: processedValue,
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: processedValue,
      }));
    }
  };

  const handleAssignmentChange = () => {
    setRefreshKey((prev) => prev + 1);
  };

  const getBusNumber = (busId) => {
    const bus = buses.find((b) => b.id === busId);
    return bus ? bus.busNumber : "No Bus";
  };

  const getRouteName = (routeId) => {
    const route = routes.find((r) => r.id === routeId);
    return route ? route.name : "No Route";
  };

  if (driversLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Driver Management
          </h1>
          <p className="text-gray-600 mt-2">
            Manage drivers and their assignments
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAssignments(!showAssignments)}
            className="group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-xl flex items-center space-x-3 hover:shadow-lg transition-all duration-200 hover:transform hover:scale-105"
          >
            <Link className="h-5 w-5 group-hover:rotate-12 transition-transform duration-200" />
            <span className="font-semibold">Assignments</span>
          </button>
          <button
            onClick={() => setShowForm(true)}
            className="group bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-xl flex items-center space-x-3 hover:shadow-lg transition-all duration-200 hover:transform hover:scale-105"
          >
            <Plus className="h-5 w-5 group-hover:rotate-90 transition-transform duration-200" />
            <span className="font-semibold">Add Driver</span>
          </button>
        </div>
      </div>

      {/* Drivers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {drivers.map((driver) => (
          <div key={driver.id} className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <User className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">
                    {driver.name || "Unknown Driver"}
                  </h3>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      STATUS_COLORS[driver.status] ||
                      "text-gray-600 bg-gray-100"
                    }`}
                  >
                    {driver.status || "off_duty"}
                  </span>
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleEdit(driver)}
                  className="p-1 text-gray-400 hover:text-blue-600"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete(driver.id)}
                  className="p-1 text-gray-400 hover:text-red-600"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">License</span>
                <span className="font-medium text-sm">
                  {driver.licenseNumber || "N/A"}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Phone</span>
                <div className="flex items-center space-x-1">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-medium">
                    {driver.phoneNumber || "N/A"}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Email</span>
                <div className="flex items-center space-x-1">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-medium truncate">
                    {driver.email || "N/A"}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Assigned Bus</span>
                <div className="flex items-center space-x-1">
                  <Bus className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-medium">
                    {getBusNumber(driver.assignedBusId)}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Route</span>
                <div className="flex items-center space-x-1">
                  <Route className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-medium">
                    {getRouteName(driver.assignedRouteId)}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Location</span>
                <div className="flex items-center space-x-1">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-mono">
                    {typeof driver.currentLocation?.latitude === "number"
                      ? driver.currentLocation.latitude.toFixed(4)
                      : "0.0000"}
                    ,{" "}
                    {typeof driver.currentLocation?.longitude === "number"
                      ? driver.currentLocation.longitude.toFixed(4)
                      : "0.0000"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add/Edit Driver Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-screen overflow-y-auto">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              {editingDriver ? "Edit Driver" : "Add New Driver"}
            </h2>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Driver Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  License Number
                </label>
                <input
                  type="text"
                  name="licenseNumber"
                  value={formData.licenseNumber}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <input
                  type="tel"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {Object.entries(DRIVER_STATUS).map(([key, value]) => (
                    <option key={key} value={value}>
                      {value.replace("_", " ").toUpperCase()}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Assigned Bus
                </label>
                <select
                  name="assignedBusId"
                  value={formData.assignedBusId}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Bus</option>
                  {buses.map((bus) => (
                    <option key={bus.id} value={bus.id}>
                      Bus #{bus.busNumber}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Assigned Route
                </label>
                <select
                  name="assignedRouteId"
                  value={formData.assignedRouteId}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Route</option>
                  {routes.map((route) => (
                    <option key={route.id} value={route.id}>
                      {route.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
                >
                  {editingDriver ? "Update Driver" : "Add Driver"}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowForm(false);
                    setEditingDriver(null);
                    setFormData(DEFAULT_DRIVER);
                  }}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Driver-Bus Assignment Component */}
      {showAssignments && (
        <div className="mt-8">
          <DriverBusAssignment
            key={refreshKey}
            onAssignmentChange={handleAssignmentChange}
          />
        </div>
      )}
    </div>
  );
};

export default DriverManager;
