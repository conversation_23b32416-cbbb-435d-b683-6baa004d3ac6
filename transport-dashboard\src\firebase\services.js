import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc,
  onSnapshot,
  query,
  orderBy,
  where,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from './config';

// Collection names
export const COLLECTIONS = {
  BUSES: 'buses',
  DRIVERS: 'drivers',
  STATIONS: 'stations',
  ROUTES: 'routes'
};

// Generic CRUD operations
export const createDocument = async (collectionName, data) => {
  try {
    const docRef = await addDoc(collection(db, collectionName), {
      ...data,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error creating document:', error);
    throw error;
  }
};

export const updateDocument = async (collectionName, docId, data) => {
  try {
    const docRef = doc(db, collectionName, docId);
    await updateDoc(docRef, {
      ...data,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error updating document:', error);
    throw error;
  }
};

export const deleteDocument = async (collectionName, docId) => {
  try {
    await deleteDoc(doc(db, collectionName, docId));
  } catch (error) {
    console.error('Error deleting document:', error);
    throw error;
  }
};

export const getDocument = async (collectionName, docId) => {
  try {
    const docRef = doc(db, collectionName, docId);
    const docSnap = await getDoc(docRef);
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() };
    }
    return null;
  } catch (error) {
    console.error('Error getting document:', error);
    throw error;
  }
};

export const getAllDocuments = async (collectionName) => {
  try {
    const querySnapshot = await getDocs(collection(db, collectionName));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting documents:', error);
    throw error;
  }
};

// Real-time listeners
export const subscribeToCollection = (collectionName, callback, orderByField = 'createdAt') => {
  const q = query(collection(db, collectionName), orderBy(orderByField));
  return onSnapshot(q, (querySnapshot) => {
    const documents = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    callback(documents);
  });
};

export const subscribeToDocument = (collectionName, docId, callback) => {
  const docRef = doc(db, collectionName, docId);
  return onSnapshot(docRef, (docSnap) => {
    if (docSnap.exists()) {
      callback({ id: docSnap.id, ...docSnap.data() });
    } else {
      callback(null);
    }
  });
};

// Specific service functions for transport entities

// Bus services
export const createBus = (busData) => createDocument(COLLECTIONS.BUSES, busData);
export const updateBus = (busId, busData) => updateDocument(COLLECTIONS.BUSES, busId, busData);
export const deleteBus = (busId) => deleteDocument(COLLECTIONS.BUSES, busId);
export const getBus = (busId) => getDocument(COLLECTIONS.BUSES, busId);
export const getAllBuses = () => getAllDocuments(COLLECTIONS.BUSES);
export const subscribeToBuses = (callback) => subscribeToCollection(COLLECTIONS.BUSES, callback);

// Driver services
export const createDriver = (driverData) => createDocument(COLLECTIONS.DRIVERS, driverData);
export const updateDriver = (driverId, driverData) => updateDocument(COLLECTIONS.DRIVERS, driverId, driverData);
export const deleteDriver = (driverId) => deleteDocument(COLLECTIONS.DRIVERS, driverId);
export const getDriver = (driverId) => getDocument(COLLECTIONS.DRIVERS, driverId);
export const getAllDrivers = () => getAllDocuments(COLLECTIONS.DRIVERS);
export const subscribeToDrivers = (callback) => subscribeToCollection(COLLECTIONS.DRIVERS, callback);

// Station services
export const createStation = (stationData) => createDocument(COLLECTIONS.STATIONS, stationData);
export const updateStation = (stationId, stationData) => updateDocument(COLLECTIONS.STATIONS, stationId, stationData);
export const deleteStation = (stationId) => deleteDocument(COLLECTIONS.STATIONS, stationId);
export const getStation = (stationId) => getDocument(COLLECTIONS.STATIONS, stationId);
export const getAllStations = () => getAllDocuments(COLLECTIONS.STATIONS);
export const subscribeToStations = (callback) => subscribeToCollection(COLLECTIONS.STATIONS, callback);

// Route services
export const createRoute = (routeData) => createDocument(COLLECTIONS.ROUTES, routeData);
export const updateRoute = (routeId, routeData) => updateDocument(COLLECTIONS.ROUTES, routeId, routeData);
export const deleteRoute = (routeId) => deleteDocument(COLLECTIONS.ROUTES, routeId);
export const getRoute = (routeId) => getDocument(COLLECTIONS.ROUTES, routeId);
export const getAllRoutes = () => getAllDocuments(COLLECTIONS.ROUTES);
export const subscribeToRoutes = (callback) => subscribeToCollection(COLLECTIONS.ROUTES, callback);

// Query functions for specific use cases
export const getBusesByRoute = async (routeId) => {
  try {
    const q = query(collection(db, COLLECTIONS.BUSES), where('routeId', '==', routeId));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting buses by route:', error);
    throw error;
  }
};

export const getDriversByStatus = async (status) => {
  try {
    const q = query(collection(db, COLLECTIONS.DRIVERS), where('status', '==', status));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting drivers by status:', error);
    throw error;
  }
};

// Assignment functions
export const assignDriverToBus = async (driverId, busId) => {
  try {
    // Update driver with assigned bus
    await updateDocument(COLLECTIONS.DRIVERS, driverId, {
      assignedBusId: busId,
      status: 'on_duty'
    });

    // Update bus with assigned driver
    await updateDocument(COLLECTIONS.BUSES, busId, {
      driverId: driverId,
      status: 'active'
    });

    return true;
  } catch (error) {
    console.error('Error assigning driver to bus:', error);
    throw error;
  }
};

export const unassignDriverFromBus = async (driverId, busId) => {
  try {
    // Update driver to remove bus assignment
    await updateDocument(COLLECTIONS.DRIVERS, driverId, {
      assignedBusId: '',
      status: 'available'
    });

    // Update bus to remove driver assignment
    await updateDocument(COLLECTIONS.BUSES, busId, {
      driverId: '',
      status: 'inactive'
    });

    return true;
  } catch (error) {
    console.error('Error unassigning driver from bus:', error);
    throw error;
  }
};

export const assignBusToRoute = async (busId, routeId) => {
  try {
    // Update bus with assigned route
    await updateDocument(COLLECTIONS.BUSES, busId, {
      routeId: routeId
    });

    return true;
  } catch (error) {
    console.error('Error assigning bus to route:', error);
    throw error;
  }
};

export const unassignBusFromRoute = async (busId) => {
  try {
    // Update bus to remove route assignment
    await updateDocument(COLLECTIONS.BUSES, busId, {
      routeId: ''
    });

    return true;
  } catch (error) {
    console.error('Error unassigning bus from route:', error);
    throw error;
  }
};

// Get available (unassigned) drivers
export const getAvailableDrivers = async () => {
  try {
    const q = query(
      collection(db, COLLECTIONS.DRIVERS),
      where('assignedBusId', '==', ''),
      where('status', 'in', ['available', 'off_duty'])
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting available drivers:', error);
    throw error;
  }
};

// Get available (unassigned) buses
export const getAvailableBuses = async () => {
  try {
    const q = query(
      collection(db, COLLECTIONS.BUSES),
      where('driverId', '==', '')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting available buses:', error);
    throw error;
  }
};

// Update passenger count at station
export const updateStationPassengerCount = async (stationId, count) => {
  try {
    await updateDocument(COLLECTIONS.STATIONS, stationId, {
      currentPassengerCount: count
    });
    return true;
  } catch (error) {
    console.error('Error updating station passenger count:', error);
    throw error;
  }
};
