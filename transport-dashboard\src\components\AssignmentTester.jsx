import React, { useState, useEffect } from 'react';
import {
  assignDriverToBus,
  unassignDriverFromBus,
  assignBusToRoute,
  unassignBusFromRoute,
  getAvailableDrivers,
  getAvailableBuses,
  getAllDrivers,
  getAllBuses,
  getAllRoutes
} from '../firebase/services';
import { CheckCircle, XCircle, AlertCircle, Play, User, Bus, Route } from 'lucide-react';

const AssignmentTester = () => {
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);
  const [drivers, setDrivers] = useState([]);
  const [buses, setBuses] = useState([]);
  const [routes, setRoutes] = useState([]);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [driversData, busesData, routesData] = await Promise.all([
        getAllDrivers(),
        getAllBuses(),
        getAllRoutes()
      ]);
      setDrivers(driversData);
      setBuses(busesData);
      setRoutes(routesData);
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  const addResult = (test, status, message) => {
    setTestResults(prev => ({
      ...prev,
      [test]: { status, message, timestamp: new Date().toLocaleTimeString() }
    }));
  };

  const testDriverBusAssignment = async () => {
    try {
      addResult('assignment-start', 'info', 'Starting driver-bus assignment tests...');

      // Get available drivers and buses
      const availableDrivers = await getAvailableDrivers();
      const availableBuses = await getAvailableBuses();

      addResult('available-check', 'success', 
        `Found ${availableDrivers.length} available drivers and ${availableBuses.length} available buses`);

      if (availableDrivers.length === 0 || availableBuses.length === 0) {
        addResult('assignment-skip', 'warning', 'No available drivers or buses for assignment test');
        return;
      }

      // Test assignment
      const testDriver = availableDrivers[0];
      const testBus = availableBuses[0];

      await assignDriverToBus(testDriver.id, testBus.id);
      addResult('driver-bus-assign', 'success', 
        `Successfully assigned driver ${testDriver.name} to bus ${testBus.busNumber}`);

      // Verify assignment
      const updatedDrivers = await getAllDrivers();
      const updatedBuses = await getAllBuses();
      
      const assignedDriver = updatedDrivers.find(d => d.id === testDriver.id);
      const assignedBus = updatedBuses.find(b => b.id === testBus.id);

      if (assignedDriver?.assignedBusId === testBus.id && assignedBus?.driverId === testDriver.id) {
        addResult('assignment-verify', 'success', 'Assignment verification successful');
      } else {
        addResult('assignment-verify', 'error', 'Assignment verification failed');
      }

      // Test unassignment
      await unassignDriverFromBus(testDriver.id, testBus.id);
      addResult('driver-bus-unassign', 'success', 
        `Successfully unassigned driver ${testDriver.name} from bus ${testBus.busNumber}`);

    } catch (error) {
      addResult('assignment-error', 'error', `Assignment test failed: ${error.message}`);
    }
  };

  const testBusRouteAssignment = async () => {
    try {
      addResult('route-assignment-start', 'info', 'Starting bus-route assignment tests...');

      if (buses.length === 0 || routes.length === 0) {
        addResult('route-assignment-skip', 'warning', 'No buses or routes available for assignment test');
        return;
      }

      // Test route assignment
      const testBus = buses[0];
      const testRoute = routes[0];

      await assignBusToRoute(testBus.id, testRoute.id);
      addResult('bus-route-assign', 'success', 
        `Successfully assigned bus ${testBus.busNumber} to route ${testRoute.name}`);

      // Verify assignment
      const updatedBuses = await getAllBuses();
      const assignedBus = updatedBuses.find(b => b.id === testBus.id);

      if (assignedBus?.routeId === testRoute.id) {
        addResult('route-assignment-verify', 'success', 'Route assignment verification successful');
      } else {
        addResult('route-assignment-verify', 'error', 'Route assignment verification failed');
      }

      // Test unassignment
      await unassignBusFromRoute(testBus.id);
      addResult('bus-route-unassign', 'success', 
        `Successfully unassigned bus ${testBus.busNumber} from route ${testRoute.name}`);

    } catch (error) {
      addResult('route-assignment-error', 'error', `Route assignment test failed: ${error.message}`);
    }
  };

  const runAllAssignmentTests = async () => {
    setIsRunning(true);
    setTestResults({});

    await loadData();
    await testDriverBusAssignment();
    await testBusRouteAssignment();

    addResult('test-complete', 'success', 'All assignment tests completed!');
    setIsRunning(false);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-blue-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Assignment System Tester
            </h2>
            <p className="text-gray-600 mt-1">
              Test driver-to-bus and bus-to-route assignment functionality
            </p>
          </div>
          <button
            onClick={runAllAssignmentTests}
            disabled={isRunning}
            className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Play className="h-5 w-5" />
            <span>
              {isRunning ? "Running Tests..." : "Run Assignment Tests"}
            </span>
          </button>
        </div>

        {/* System Status */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <User className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-900">Drivers</span>
              </div>
              <span className="text-2xl font-bold text-blue-600">{drivers.length}</span>
            </div>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Bus className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-900">Buses</span>
              </div>
              <span className="text-2xl font-bold text-green-600">{buses.length}</span>
            </div>
          </div>
          <div className="bg-purple-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Route className="h-5 w-5 text-purple-600" />
                <span className="font-medium text-purple-900">Routes</span>
              </div>
              <span className="text-2xl font-bold text-purple-600">{routes.length}</span>
            </div>
          </div>
        </div>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-gray-900">Test Results</h3>
            <div className="space-y-2">
              {Object.entries(testResults).map(([test, result]) => (
                <div
                  key={test}
                  className={`flex items-center space-x-3 p-3 rounded-lg border ${getStatusColor(result.status)}`}
                >
                  {getStatusIcon(result.status)}
                  <div className="flex-1">
                    <p className="font-medium">{result.message}</p>
                    <p className="text-sm opacity-75">{result.timestamp}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AssignmentTester;
