import { 
  createStation, 
  createDriver, 
  createBus, 
  createRoute 
} from '../firebase/services';
import { BUS_STATUS, DRIVER_STATUS, ROUTE_STATUS, STATION_TYPES } from './constants';

// Sample stations data
const sampleStations = [
  {
    name: "Central Station",
    location: { latitude: 40.7128, longitude: -74.0060 },
    type: STATION_TYPES.TERMINAL,
    currentPassengerCount: 25,
    maxCapacity: 150,
    amenities: ["WiFi", "Restrooms", "Food Court"]
  },
  {
    name: "Downtown Hub",
    location: { latitude: 40.7589, longitude: -73.9851 },
    type: STATION_TYPES.INTERCHANGE,
    currentPassengerCount: 18,
    maxCapacity: 100,
    amenities: ["WiFi", "Restrooms"]
  },
  {
    name: "University Stop",
    location: { latitude: 40.7505, longitude: -73.9934 },
    type: STATION_TYPES.BUS_STOP,
    currentPassengerCount: 12,
    maxCapacity: 50,
    amenities: ["WiFi"]
  },
  {
    name: "Shopping Mall",
    location: { latitude: 40.7282, longitude: -73.7949 },
    type: STATION_TYPES.BUS_STOP,
    currentPassengerCount: 8,
    maxCapacity: 75,
    amenities: ["WiFi", "Restrooms", "Shopping"]
  },
  {
    name: "Airport Terminal",
    location: { latitude: 40.6413, longitude: -73.7781 },
    type: STATION_TYPES.TERMINAL,
    currentPassengerCount: 35,
    maxCapacity: 200,
    amenities: ["WiFi", "Restrooms", "Food Court", "Luggage Storage"]
  },
  {
    name: "Business District",
    location: { latitude: 40.7614, longitude: -73.9776 },
    type: STATION_TYPES.BUS_STOP,
    currentPassengerCount: 22,
    maxCapacity: 80,
    amenities: ["WiFi"]
  }
];

// Sample drivers data
const sampleDrivers = [
  {
    name: "John Smith",
    licenseNumber: "DL123456789",
    phoneNumber: "******-0101",
    email: "<EMAIL>",
    status: DRIVER_STATUS.ON_DUTY,
    currentLocation: { latitude: 40.7128, longitude: -74.0060 },
    assignedBusId: "",
    assignedRouteId: ""
  },
  {
    name: "Maria Garcia",
    licenseNumber: "DL987654321",
    phoneNumber: "******-0102",
    email: "<EMAIL>",
    status: DRIVER_STATUS.AVAILABLE,
    currentLocation: { latitude: 40.7589, longitude: -73.9851 },
    assignedBusId: "",
    assignedRouteId: ""
  },
  {
    name: "David Johnson",
    licenseNumber: "DL456789123",
    phoneNumber: "******-0103",
    email: "<EMAIL>",
    status: DRIVER_STATUS.ON_DUTY,
    currentLocation: { latitude: 40.7505, longitude: -73.9934 },
    assignedBusId: "",
    assignedRouteId: ""
  },
  {
    name: "Sarah Wilson",
    licenseNumber: "DL789123456",
    phoneNumber: "******-0104",
    email: "<EMAIL>",
    status: DRIVER_STATUS.BREAK,
    currentLocation: { latitude: 40.7282, longitude: -73.7949 },
    assignedBusId: "",
    assignedRouteId: ""
  },
  {
    name: "Michael Brown",
    licenseNumber: "DL321654987",
    phoneNumber: "******-0105",
    email: "<EMAIL>",
    status: DRIVER_STATUS.OFF_DUTY,
    currentLocation: { latitude: 40.6413, longitude: -73.7781 },
    assignedBusId: "",
    assignedRouteId: ""
  }
];

// Sample buses data
const sampleBuses = [
  {
    busNumber: "BUS001",
    capacity: 50,
    currentLocation: { latitude: 40.7128, longitude: -74.0060 },
    status: BUS_STATUS.ACTIVE,
    routeId: "",
    driverId: "",
    lastUpdated: new Date()
  },
  {
    busNumber: "BUS002",
    capacity: 45,
    currentLocation: { latitude: 40.7589, longitude: -73.9851 },
    status: BUS_STATUS.ACTIVE,
    routeId: "",
    driverId: "",
    lastUpdated: new Date()
  },
  {
    busNumber: "BUS003",
    capacity: 55,
    currentLocation: { latitude: 40.7505, longitude: -73.9934 },
    status: BUS_STATUS.MAINTENANCE,
    routeId: "",
    driverId: "",
    lastUpdated: new Date()
  },
  {
    busNumber: "BUS004",
    capacity: 40,
    currentLocation: { latitude: 40.7282, longitude: -73.7949 },
    status: BUS_STATUS.INACTIVE,
    routeId: "",
    driverId: "",
    lastUpdated: new Date()
  },
  {
    busNumber: "BUS005",
    capacity: 60,
    currentLocation: { latitude: 40.6413, longitude: -73.7781 },
    status: BUS_STATUS.ACTIVE,
    routeId: "",
    driverId: "",
    lastUpdated: new Date()
  }
];

// Function to seed sample data
export const seedSampleData = async () => {
  try {
    console.log('Starting to seed sample data...');
    
    // Create stations first
    console.log('Creating stations...');
    const stationIds = [];
    for (const station of sampleStations) {
      const stationId = await createStation(station);
      stationIds.push(stationId);
      console.log(`Created station: ${station.name}`);
    }

    // Create drivers
    console.log('Creating drivers...');
    const driverIds = [];
    for (const driver of sampleDrivers) {
      const driverId = await createDriver(driver);
      driverIds.push(driverId);
      console.log(`Created driver: ${driver.name}`);
    }

    // Create buses
    console.log('Creating buses...');
    const busIds = [];
    for (const bus of sampleBuses) {
      const busId = await createBus(bus);
      busIds.push(busId);
      console.log(`Created bus: ${bus.busNumber}`);
    }

    // Create sample routes
    console.log('Creating routes...');
    const sampleRoutes = [
      {
        name: "Downtown Express",
        description: "Fast route connecting downtown area with central station",
        stations: stationIds.slice(0, 3), // First 3 stations
        status: ROUTE_STATUS.ACTIVE,
        estimatedDuration: 45,
        frequency: 15
      },
      {
        name: "Airport Shuttle",
        description: "Direct connection to airport terminal",
        stations: [stationIds[0], stationIds[4]], // Central Station to Airport
        status: ROUTE_STATUS.ACTIVE,
        estimatedDuration: 30,
        frequency: 20
      },
      {
        name: "University Circuit",
        description: "Route serving university and surrounding areas",
        stations: stationIds.slice(2, 5), // University, Mall, Airport
        status: ROUTE_STATUS.ACTIVE,
        estimatedDuration: 35,
        frequency: 12
      }
    ];

    for (const route of sampleRoutes) {
      const routeId = await createRoute(route);
      console.log(`Created route: ${route.name}`);
    }

    console.log('Sample data seeding completed successfully!');
    return {
      success: true,
      message: 'Sample data has been created successfully!'
    };
  } catch (error) {
    console.error('Error seeding sample data:', error);
    return {
      success: false,
      message: 'Failed to create sample data: ' + error.message
    };
  }
};
