import React, { useState } from "react";
import {
  useRealTimeRoutes,
  useRealTimeStations,
} from "../hooks/useRealTimeData";
import { createRoute, updateRoute, deleteRoute } from "../firebase/services";
import { DEFAULT_ROUTE, ROUTE_STATUS, STATUS_COLORS } from "../utils/constants";
import {
  Route,
  Plus,
  Edit,
  Trash2,
  MapPin,
  Clock,
  ArrowRight,
} from "lucide-react";

const RouteManager = () => {
  const { routes, loading: routesLoading } = useRealTimeRoutes();
  const { stations } = useRealTimeStations();
  const [showForm, setShowForm] = useState(false);
  const [editingRoute, setEditingRoute] = useState(null);
  const [formData, setFormData] = useState(DEFAULT_ROUTE);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingRoute) {
        await updateRoute(editingRoute.id, formData);
      } else {
        await createRoute(formData);
      }
      setShowForm(false);
      setEditingRoute(null);
      setFormData(DEFAULT_ROUTE);
    } catch (error) {
      console.error("Error saving route:", error);
    }
  };

  const handleEdit = (route) => {
    setEditingRoute(route);
    setFormData(route);
    setShowForm(true);
  };

  const handleDelete = async (routeId) => {
    if (window.confirm("Are you sure you want to delete this route?")) {
      try {
        await deleteRoute(routeId);
      } catch (error) {
        console.error("Error deleting route:", error);
      }
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    const processedValue =
      type === "number" ? (value === "" ? 0 : Number(value)) : value;

    setFormData((prev) => ({
      ...prev,
      [name]: processedValue,
    }));
  };

  const handleStationSelection = (stationId) => {
    setFormData((prev) => ({
      ...prev,
      stations: prev.stations.includes(stationId)
        ? prev.stations.filter((id) => id !== stationId)
        : [...prev.stations, stationId],
    }));
  };

  const getStationName = (stationId) => {
    const station = stations.find((s) => s.id === stationId);
    return station ? station.name : "Unknown Station";
  };

  const moveStation = (index, direction) => {
    const newStations = [...formData.stations];
    const newIndex = direction === "up" ? index - 1 : index + 1;

    if (newIndex >= 0 && newIndex < newStations.length) {
      [newStations[index], newStations[newIndex]] = [
        newStations[newIndex],
        newStations[index],
      ];
      setFormData((prev) => ({ ...prev, stations: newStations }));
    }
  };

  if (routesLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Route Management</h1>
          <p className="text-gray-600 mt-2">Create and manage bus routes</p>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="group bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-xl flex items-center space-x-3 hover:shadow-lg transition-all duration-200 hover:transform hover:scale-105"
        >
          <Plus className="h-5 w-5 group-hover:rotate-90 transition-transform duration-200" />
          <span className="font-semibold">Add Route</span>
        </button>
      </div>

      {/* Routes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {routes.map((route) => (
          <div key={route.id} className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Route className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">
                    {route.name || "Unnamed Route"}
                  </h3>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      STATUS_COLORS[route.status] || "text-gray-600 bg-gray-100"
                    }`}
                  >
                    {route.status || "inactive"}
                  </span>
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleEdit(route)}
                  className="p-1 text-gray-400 hover:text-blue-600"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete(route.id)}
                  className="p-1 text-gray-400 hover:text-red-600"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Stations</span>
                <span className="font-medium">
                  {route.stations?.length || 0}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Duration</span>
                <div className="flex items-center space-x-1">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="font-medium">
                    {route.estimatedDuration || 0} min
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Frequency</span>
                <span className="font-medium">
                  Every {route.frequency || 15} min
                </span>
              </div>

              {route.description && (
                <div className="mt-3">
                  <p className="text-sm text-gray-600">{route.description}</p>
                </div>
              )}

              {/* Station List Preview */}
              {route.stations && route.stations.length > 0 && (
                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">
                    Route Stations:
                  </h4>
                  <div className="space-y-1">
                    {route.stations.slice(0, 3).map((stationId, index) => (
                      <div
                        key={stationId}
                        className="flex items-center space-x-2 text-sm"
                      >
                        <span className="text-gray-500">{index + 1}.</span>
                        <MapPin className="h-3 w-3 text-gray-400" />
                        <span className="text-gray-700">
                          {getStationName(stationId)}
                        </span>
                      </div>
                    ))}
                    {route.stations.length > 3 && (
                      <div className="text-sm text-gray-500">
                        +{route.stations.length - 3} more stations
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Add/Edit Route Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-screen overflow-y-auto">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              {editingRoute ? "Edit Route" : "Add New Route"}
            </h2>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Route Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {Object.entries(ROUTE_STATUS).map(([key, value]) => (
                      <option key={key} value={value}>
                        {value.replace("_", " ").toUpperCase()}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows="3"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Estimated Duration (minutes)
                  </label>
                  <input
                    type="number"
                    name="estimatedDuration"
                    value={formData.estimatedDuration}
                    onChange={handleInputChange}
                    min="1"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Frequency (minutes)
                  </label>
                  <input
                    type="number"
                    name="frequency"
                    value={formData.frequency}
                    onChange={handleInputChange}
                    min="1"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Station Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Route Stations (Select in order)
                </label>

                {/* Selected Stations */}
                {formData.stations.length > 0 && (
                  <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                    <h4 className="text-sm font-medium text-blue-900 mb-2">
                      Selected Stations:
                    </h4>
                    <div className="space-y-2">
                      {formData.stations.map((stationId, index) => (
                        <div
                          key={stationId}
                          className="flex items-center justify-between bg-white p-2 rounded"
                        >
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-blue-600">
                              {index + 1}.
                            </span>
                            <span className="text-sm">
                              {getStationName(stationId)}
                            </span>
                          </div>
                          <div className="flex space-x-1">
                            <button
                              type="button"
                              onClick={() => moveStation(index, "up")}
                              disabled={index === 0}
                              className="p-1 text-gray-400 hover:text-blue-600 disabled:opacity-50"
                            >
                              ↑
                            </button>
                            <button
                              type="button"
                              onClick={() => moveStation(index, "down")}
                              disabled={index === formData.stations.length - 1}
                              className="p-1 text-gray-400 hover:text-blue-600 disabled:opacity-50"
                            >
                              ↓
                            </button>
                            <button
                              type="button"
                              onClick={() => handleStationSelection(stationId)}
                              className="p-1 text-gray-400 hover:text-red-600"
                            >
                              ×
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Available Stations */}
                <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-lg">
                  {stations.map((station) => (
                    <div
                      key={station.id}
                      className={`p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-50 ${
                        formData.stations.includes(station.id)
                          ? "bg-blue-50"
                          : ""
                      }`}
                      onClick={() => handleStationSelection(station.id)}
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">
                          {station.name}
                        </span>
                        <input
                          type="checkbox"
                          checked={formData.stations.includes(station.id)}
                          onChange={() => {}}
                          className="rounded"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
                >
                  {editingRoute ? "Update Route" : "Add Route"}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowForm(false);
                    setEditingRoute(null);
                    setFormData(DEFAULT_ROUTE);
                  }}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default RouteManager;
