import React, { useState } from "react";
import { useRealTimeStations } from "../hooks/useRealTimeData";
import {
  createStation,
  updateStation,
  deleteStation,
  updateStationPassengerCount,
} from "../firebase/services";
import { DEFAULT_STATION, STATION_TYPES } from "../utils/constants";
import {
  MapPin,
  Plus,
  Edit,
  Trash2,
  Users,
  Settings,
  Minus,
  UserPlus,
  UserMinus,
} from "lucide-react";

const StationManager = () => {
  const { stations, loading } = useRealTimeStations();
  const [showForm, setShowForm] = useState(false);
  const [editingStation, setEditingStation] = useState(null);
  const [formData, setFormData] = useState(DEFAULT_STATION);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingStation) {
        await updateStation(editingStation.id, formData);
      } else {
        await createStation(formData);
      }
      setShowForm(false);
      setEditingStation(null);
      setFormData(DEFAULT_STATION);
    } catch (error) {
      console.error("Error saving station:", error);
    }
  };

  const handleEdit = (station) => {
    setEditingStation(station);
    setFormData(station);
    setShowForm(true);
  };

  const handleDelete = async (stationId) => {
    if (window.confirm("Are you sure you want to delete this station?")) {
      try {
        await deleteStation(stationId);
      } catch (error) {
        console.error("Error deleting station:", error);
      }
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    const processedValue =
      type === "number" ? (value === "" ? 0 : Number(value)) : value;

    if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setFormData((prev) => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: processedValue,
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: processedValue,
      }));
    }
  };

  const handlePassengerCountChange = async (stationId, change) => {
    try {
      const station = stations.find((s) => s.id === stationId);
      if (!station) return;

      const newCount = Math.max(
        0,
        (station.currentPassengerCount || 0) + change
      );
      const maxCapacity = station.maxCapacity || 100;

      // Don't exceed max capacity
      if (newCount <= maxCapacity) {
        await updateStationPassengerCount(stationId, newCount);
      }
    } catch (error) {
      console.error("Error updating passenger count:", error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Station Management
          </h1>
          <p className="text-gray-600 mt-2">
            Manage bus stations and monitor passenger traffic
          </p>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="btn-primary flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Add Station</span>
        </button>
      </div>

      {/* Stations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
        {stations.map((station) => (
          <div
            key={station.id}
            className="group bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-6 border border-white/20 hover:shadow-xl transition-all duration-300 hover:transform hover:scale-[1.02]"
          >
            <div className="flex items-start justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg">
                  <MapPin className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900">
                    {station.name || "Unnamed Station"}
                  </h3>
                  <p className="text-sm text-gray-600 capitalize font-medium">
                    {station.type?.replace("_", " ") || "bus stop"}
                  </p>
                </div>
              </div>
              <div className="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <button
                  onClick={() => handleEdit(station)}
                  className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete(station.id)}
                  className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="space-y-4">
              {/* Passenger Count with Controls */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-gray-700">
                    Waiting Passengers
                  </span>
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4 text-gray-400" />
                    <span className="font-bold text-lg">
                      {station.currentPassengerCount || 0}
                    </span>
                  </div>
                </div>

                {/* Passenger Count Controls */}
                <div className="flex items-center justify-center space-x-2">
                  <button
                    onClick={() => handlePassengerCountChange(station.id, -1)}
                    className="btn-icon bg-red-50 text-red-600 hover:bg-red-100"
                    disabled={(station.currentPassengerCount || 0) <= 0}
                  >
                    <UserMinus className="h-4 w-4" />
                  </button>

                  <button
                    onClick={() => handlePassengerCountChange(station.id, -5)}
                    className="btn-sm bg-red-50 text-red-600 hover:bg-red-100 border border-red-200"
                    disabled={(station.currentPassengerCount || 0) < 5}
                  >
                    -5
                  </button>

                  <button
                    onClick={() => handlePassengerCountChange(station.id, 1)}
                    className="btn-icon bg-green-50 text-green-600 hover:bg-green-100"
                    disabled={
                      (station.currentPassengerCount || 0) >=
                      (station.maxCapacity || 100)
                    }
                  >
                    <UserPlus className="h-4 w-4" />
                  </button>

                  <button
                    onClick={() => handlePassengerCountChange(station.id, 5)}
                    className="btn-sm bg-green-50 text-green-600 hover:bg-green-100 border border-green-200"
                    disabled={
                      (station.currentPassengerCount || 0) + 5 >
                      (station.maxCapacity || 100)
                    }
                  >
                    +5
                  </button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Max Capacity</span>
                <span className="font-medium">
                  {station.maxCapacity || 100}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Location</span>
                <span className="text-sm font-mono">
                  {typeof station.location?.latitude === "number"
                    ? station.location.latitude.toFixed(4)
                    : "0.0000"}
                  ,{" "}
                  {typeof station.location?.longitude === "number"
                    ? station.location.longitude.toFixed(4)
                    : "0.0000"}
                </span>
              </div>

              {/* Capacity Bar */}
              <div className="mt-4">
                <div className="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Capacity</span>
                  <span>
                    {Math.round(
                      ((station.currentPassengerCount || 0) /
                        (station.maxCapacity || 100)) *
                        100
                    )}
                    %
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      (station.currentPassengerCount || 0) /
                        (station.maxCapacity || 100) >
                      0.8
                        ? "bg-red-500"
                        : (station.currentPassengerCount || 0) /
                            (station.maxCapacity || 100) >
                          0.6
                        ? "bg-yellow-500"
                        : "bg-green-500"
                    }`}
                    style={{
                      width: `${Math.min(
                        ((station.currentPassengerCount || 0) /
                          (station.maxCapacity || 100)) *
                          100,
                        100
                      )}%`,
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add/Edit Station Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              {editingStation ? "Edit Station" : "Add New Station"}
            </h2>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Station Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Station Type
                </label>
                <select
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {Object.entries(STATION_TYPES).map(([key, value]) => (
                    <option key={key} value={value}>
                      {value.replace("_", " ").toUpperCase()}
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Latitude
                  </label>
                  <input
                    type="number"
                    name="location.latitude"
                    value={formData.location.latitude}
                    onChange={handleInputChange}
                    step="any"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Longitude
                  </label>
                  <input
                    type="number"
                    name="location.longitude"
                    value={formData.location.longitude}
                    onChange={handleInputChange}
                    step="any"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Capacity
                </label>
                <input
                  type="number"
                  name="maxCapacity"
                  value={formData.maxCapacity}
                  onChange={handleInputChange}
                  min="1"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button type="submit" className="flex-1 btn-primary">
                  {editingStation ? "Update Station" : "Add Station"}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowForm(false);
                    setEditingStation(null);
                    setFormData(DEFAULT_STATION);
                  }}
                  className="flex-1 btn-secondary"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default StationManager;
