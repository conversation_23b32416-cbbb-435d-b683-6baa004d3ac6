import React, { useState } from "react";
import {
  createStation,
  updateStation,
  deleteStation,
  getAllStations,
  createBus,
  updateBus,
  deleteBus,
  getAllBuses,
  createDriver,
  updateDriver,
  deleteDriver,
  getAllDrivers,
  createRoute,
  updateRoute,
  deleteRoute,
  getAllRoutes,
} from "../firebase/services";
import { CheckCircle, XCircle, AlertCircle, Play } from "lucide-react";

const CRUDTester = () => {
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (test, status, message) => {
    setTestResults((prev) => ({
      ...prev,
      [test]: { status, message, timestamp: new Date().toLocaleTimeString() },
    }));
  };

  const runStationCRUDTest = async () => {
    try {
      // CREATE Test
      const testStation = {
        name: "Test Station CRUD",
        location: { latitude: 40.7128, longitude: -74.006 },
        type: "bus_stop",
        currentPassengerCount: 5,
        maxCapacity: 50,
      };

      const stationId = await createStation(testStation);
      addResult(
        "station-create",
        "success",
        `Station created with ID: ${stationId}`
      );

      // READ Test
      const stations = await getAllStations();
      const createdStation = stations.find((s) => s.id === stationId);
      if (createdStation) {
        addResult(
          "station-read",
          "success",
          `Station found: ${createdStation.name}`
        );
      } else {
        addResult("station-read", "error", "Station not found after creation");
        return;
      }

      // UPDATE Test
      const updateData = {
        name: "Updated Test Station",
        currentPassengerCount: 10,
      };
      await updateStation(stationId, updateData);
      addResult("station-update", "success", "Station updated successfully");

      // DELETE Test
      await deleteStation(stationId);
      addResult("station-delete", "success", "Station deleted successfully");
    } catch (error) {
      addResult(
        "station-crud",
        "error",
        `Station CRUD failed: ${error.message}`
      );
    }
  };

  const runBusCRUDTest = async () => {
    try {
      // CREATE Test
      const testBus = {
        busNumber: "TEST-001",
        capacity: 50,
        currentLocation: { latitude: 40.7128, longitude: -74.006 },
        status: "active",
      };

      const busId = await createBus(testBus);
      addResult("bus-create", "success", `Bus created with ID: ${busId}`);

      // READ Test
      const buses = await getAllBuses();
      const createdBus = buses.find((b) => b.id === busId);
      if (createdBus) {
        addResult("bus-read", "success", `Bus found: ${createdBus.busNumber}`);
      } else {
        addResult("bus-read", "error", "Bus not found after creation");
        return;
      }

      // UPDATE Test
      const updateData = {
        busNumber: "TEST-001-UPDATED",
        status: "maintenance",
      };
      await updateBus(busId, updateData);
      addResult("bus-update", "success", "Bus updated successfully");

      // DELETE Test
      await deleteBus(busId);
      addResult("bus-delete", "success", "Bus deleted successfully");
    } catch (error) {
      addResult("bus-crud", "error", `Bus CRUD failed: ${error.message}`);
    }
  };

  const runDriverCRUDTest = async () => {
    try {
      // CREATE Test
      const testDriver = {
        name: "Test Driver",
        licenseNumber: "TEST123456",
        phoneNumber: "******-TEST",
        email: "<EMAIL>",
        status: "available",
      };

      const driverId = await createDriver(testDriver);
      addResult(
        "driver-create",
        "success",
        `Driver created with ID: ${driverId}`
      );

      // READ Test
      const drivers = await getAllDrivers();
      const createdDriver = drivers.find((d) => d.id === driverId);
      if (createdDriver) {
        addResult(
          "driver-read",
          "success",
          `Driver found: ${createdDriver.name}`
        );
      } else {
        addResult("driver-read", "error", "Driver not found after creation");
        return;
      }

      // UPDATE Test
      const updateData = { name: "Updated Test Driver", status: "on_duty" };
      await updateDriver(driverId, updateData);
      addResult("driver-update", "success", "Driver updated successfully");

      // DELETE Test
      await deleteDriver(driverId);
      addResult("driver-delete", "success", "Driver deleted successfully");
    } catch (error) {
      addResult("driver-crud", "error", `Driver CRUD failed: ${error.message}`);
    }
  };

  const runRouteCRUDTest = async () => {
    try {
      // CREATE Test
      const testRoute = {
        name: "Test Route",
        description: "Test route for CRUD operations",
        stations: [],
        status: "active",
        estimatedDuration: 30,
        frequency: 15,
      };

      const routeId = await createRoute(testRoute);
      addResult("route-create", "success", `Route created with ID: ${routeId}`);

      // READ Test
      const routes = await getAllRoutes();
      const createdRoute = routes.find((r) => r.id === routeId);
      if (createdRoute) {
        addResult("route-read", "success", `Route found: ${createdRoute.name}`);
      } else {
        addResult("route-read", "error", "Route not found after creation");
        return;
      }

      // UPDATE Test
      const updateData = { name: "Updated Test Route", estimatedDuration: 45 };
      await updateRoute(routeId, updateData);
      addResult("route-update", "success", "Route updated successfully");

      // DELETE Test
      await deleteRoute(routeId);
      addResult("route-delete", "success", "Route deleted successfully");
    } catch (error) {
      addResult("route-crud", "error", `Route CRUD failed: ${error.message}`);
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults({});

    addResult("test-start", "info", "Starting CRUD tests...");

    await runStationCRUDTest();
    await runBusCRUDTest();
    await runDriverCRUDTest();
    await runRouteCRUDTest();

    addResult("test-complete", "success", "All CRUD tests completed!");
    setIsRunning(false);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "error":
        return <XCircle className="h-5 w-5 text-red-500" />;
      case "info":
        return <AlertCircle className="h-5 w-5 text-blue-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "success":
        return "bg-green-50 border-green-200 text-green-800";
      case "error":
        return "bg-red-50 border-red-200 text-red-800";
      case "info":
        return "bg-blue-50 border-blue-200 text-blue-800";
      default:
        return "bg-gray-50 border-gray-200 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              CRUD Operations Tester
            </h2>
            <p className="text-gray-600 mt-1">
              Test Create, Read, Update, Delete operations for all entities
            </p>
          </div>
          <button
            onClick={runAllTests}
            disabled={isRunning}
            className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Play className="h-5 w-5" />
            <span>{isRunning ? "Running Tests..." : "Run All Tests"}</span>
          </button>
        </div>

        {Object.keys(testResults).length > 0 && (
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-gray-900">
              Test Results:
            </h3>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {Object.entries(testResults).map(([test, result]) => (
                <div
                  key={test}
                  className={`flex items-center justify-between p-3 rounded-lg border ${getStatusColor(
                    result.status
                  )}`}
                >
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <span className="font-medium">
                        {test.replace("-", " ").toUpperCase()}
                      </span>
                      <p className="text-sm opacity-75">{result.message}</p>
                    </div>
                  </div>
                  <span className="text-xs opacity-75">{result.timestamp}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CRUDTester;
