import { useState, useEffect } from 'react';
import { 
  subscribeToBuses, 
  subscribeToDrivers, 
  subscribeToStations, 
  subscribeToRoutes 
} from '../firebase/services';

// Custom hook for real-time buses data
export const useRealTimeBuses = () => {
  const [buses, setBuses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const unsubscribe = subscribeToBuses((busesData) => {
      setBuses(busesData);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  return { buses, loading, error };
};

// Custom hook for real-time drivers data
export const useRealTimeDrivers = () => {
  const [drivers, setDrivers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const unsubscribe = subscribeToDrivers((driversData) => {
      setDrivers(driversData);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  return { drivers, loading, error };
};

// Custom hook for real-time stations data
export const useRealTimeStations = () => {
  const [stations, setStations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const unsubscribe = subscribeToStations((stationsData) => {
      setStations(stationsData);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  return { stations, loading, error };
};

// Custom hook for real-time routes data
export const useRealTimeRoutes = () => {
  const [routes, setRoutes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const unsubscribe = subscribeToRoutes((routesData) => {
      setRoutes(routesData);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  return { routes, loading, error };
};

// Combined hook for all transport data
export const useRealTimeTransportData = () => {
  const { buses, loading: busesLoading } = useRealTimeBuses();
  const { drivers, loading: driversLoading } = useRealTimeDrivers();
  const { stations, loading: stationsLoading } = useRealTimeStations();
  const { routes, loading: routesLoading } = useRealTimeRoutes();

  const loading = busesLoading || driversLoading || stationsLoading || routesLoading;

  return {
    buses,
    drivers,
    stations,
    routes,
    loading
  };
};
