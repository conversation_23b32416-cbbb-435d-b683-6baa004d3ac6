import React, { useState } from 'react';
import { seedSampleData } from '../utils/sampleData';
import { Settings, Database, Upload, CheckCircle, AlertCircle } from 'lucide-react';

const SetupHelper = () => {
  const [isSeeding, setIsSeeding] = useState(false);
  const [seedResult, setSeedResult] = useState(null);

  const handleSeedData = async () => {
    setIsSeeding(true);
    setSeedResult(null);
    
    try {
      const result = await seedSampleData();
      setSeedResult(result);
    } catch (error) {
      setSeedResult({
        success: false,
        message: 'Failed to seed data: ' + error.message
      });
    } finally {
      setIsSeeding(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Settings className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-900">Setup Helper</h2>
        </div>

        {/* Firebase Configuration Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800">Firebase Configuration Required</h3>
              <p className="text-sm text-yellow-700 mt-1">
                Before using the application, please update your Firebase configuration in 
                <code className="bg-yellow-100 px-1 rounded mx-1">src/firebase/config.js</code>
                with your actual Firebase project credentials.
              </p>
              <div className="mt-3 text-sm text-yellow-700">
                <p className="font-medium">Required fields:</p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>apiKey</li>
                  <li>authDomain</li>
                  <li>projectId</li>
                  <li>storageBucket</li>
                  <li>messagingSenderId</li>
                  <li>appId</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Sample Data Seeding */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <Database className="h-5 w-5 text-blue-600 mt-0.5" />
            <div className="flex-1">
              <h3 className="font-medium text-blue-800">Sample Data</h3>
              <p className="text-sm text-blue-700 mt-1">
                Populate your Firebase database with sample transport data including stations, 
                drivers, buses, and routes to test the application.
              </p>
              
              <div className="mt-4">
                <button
                  onClick={handleSeedData}
                  disabled={isSeeding}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSeeding ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Seeding Data...</span>
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4" />
                      <span>Seed Sample Data</span>
                    </>
                  )}
                </button>
              </div>

              {seedResult && (
                <div className={`mt-4 p-3 rounded-lg ${
                  seedResult.success 
                    ? 'bg-green-100 border border-green-200' 
                    : 'bg-red-100 border border-red-200'
                }`}>
                  <div className="flex items-center space-x-2">
                    {seedResult.success ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span className={`text-sm font-medium ${
                      seedResult.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {seedResult.message}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-6 bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-3">Getting Started</h3>
          <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
            <li>Create a Firebase project at <a href="https://console.firebase.google.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Firebase Console</a></li>
            <li>Enable Firestore Database in your Firebase project</li>
            <li>Get your Firebase configuration from Project Settings</li>
            <li>Update the configuration in <code className="bg-gray-200 px-1 rounded">src/firebase/config.js</code></li>
            <li>Click "Seed Sample Data" to populate your database</li>
            <li>Navigate to different sections using the sidebar menu</li>
          </ol>
        </div>

        {/* Features Overview */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Real-time Dashboard</h4>
            <p className="text-sm text-gray-600">
              Monitor live statistics, passenger counts, and system status with auto-updating charts and visualizations.
            </p>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Bus Management</h4>
            <p className="text-sm text-gray-600">
              Track bus locations, status, capacity, and driver assignments with real-time updates.
            </p>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Driver Management</h4>
            <p className="text-sm text-gray-600">
              Manage driver profiles, assignments, status, and track their current locations.
            </p>
          </div>
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Route Planning</h4>
            <p className="text-sm text-gray-600">
              Create and manage routes with multiple stations, schedules, and frequency settings.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SetupHelper;
