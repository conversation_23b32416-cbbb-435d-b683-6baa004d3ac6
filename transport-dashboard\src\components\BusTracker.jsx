import React, { useState } from "react";
import {
  useRealTimeBuses,
  useRealTimeDrivers,
  useRealTimeRoutes,
} from "../hooks/useRealTimeData";
import { createBus, updateBus, deleteBus } from "../firebase/services";
import { DEFAULT_BUS, BUS_STATUS, STATUS_COLORS } from "../utils/constants";
import {
  Bus,
  Plus,
  Edit,
  Trash2,
  MapPin,
  User,
  Route,
  Activity,
  Link,
} from "lucide-react";
import BusRouteAssignment from "./assignments/BusRouteAssignment";

const BusTracker = () => {
  const { buses, loading: busesLoading } = useRealTimeBuses();
  const { drivers } = useRealTimeDrivers();
  const { routes } = useRealTimeRoutes();
  const [showForm, setShowForm] = useState(false);
  const [editingBus, setEditingBus] = useState(null);
  const [formData, setFormData] = useState(DEFAULT_BUS);
  const [selectedBus, setSelectedBus] = useState(null);
  const [showAssignments, setShowAssignments] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingBus) {
        await updateBus(editingBus.id, formData);
      } else {
        await createBus(formData);
      }
      setShowForm(false);
      setEditingBus(null);
      setFormData(DEFAULT_BUS);
    } catch (error) {
      console.error("Error saving bus:", error);
    }
  };

  const handleEdit = (bus) => {
    setEditingBus(bus);
    setFormData(bus);
    setShowForm(true);
  };

  const handleDelete = async (busId) => {
    if (window.confirm("Are you sure you want to delete this bus?")) {
      try {
        await deleteBus(busId);
      } catch (error) {
        console.error("Error deleting bus:", error);
      }
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    const processedValue =
      type === "number" ? (value === "" ? 0 : Number(value)) : value;

    if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setFormData((prev) => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: processedValue,
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: processedValue,
      }));
    }
  };

  const handleAssignmentChange = () => {
    // Refresh data after assignment changes
    setSelectedBus(null);
  };

  const getDriverName = (driverId) => {
    const driver = drivers.find((d) => d.id === driverId);
    return driver ? driver.name : "Unassigned";
  };

  const getRouteName = (routeId) => {
    const route = routes.find((r) => r.id === routeId);
    return route ? route.name : "No Route";
  };

  if (busesLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Bus Fleet Management
          </h1>
          <p className="text-gray-600 mt-2">
            Monitor and manage your bus fleet in real-time
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAssignments(!showAssignments)}
            className="btn-secondary flex items-center space-x-2"
          >
            <Link className="h-4 w-4" />
            <span>Route Assignments</span>
          </button>
          <button
            onClick={() => setShowForm(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Add Bus</span>
          </button>
        </div>
      </div>

      {/* Buses Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
        {buses.map((bus) => (
          <div
            key={bus.id}
            onClick={() => setSelectedBus(bus)}
            className={`group bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-6 border transition-all duration-300 hover:transform hover:scale-[1.02] cursor-pointer ${
              selectedBus?.id === bus.id
                ? "border-purple-500 bg-purple-50/50 shadow-purple-200"
                : "border-white/20 hover:shadow-xl"
            }`}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Bus className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">
                    Bus #{bus.busNumber || "Unknown"}
                  </h3>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      STATUS_COLORS[bus.status] || "text-gray-600 bg-gray-100"
                    }`}
                  >
                    {bus.status || "inactive"}
                  </span>
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleEdit(bus)}
                  className="p-1 text-gray-400 hover:text-blue-600"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete(bus.id)}
                  className="p-1 text-gray-400 hover:text-red-600"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Capacity</span>
                <span className="font-medium">
                  {bus.capacity || 50} passengers
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Driver</span>
                <div className="flex items-center space-x-1">
                  <User className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-medium">
                    {getDriverName(bus.driverId)}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Route</span>
                <div className="flex items-center space-x-1">
                  <Route className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-medium">
                    {getRouteName(bus.routeId)}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Location</span>
                <div className="flex items-center space-x-1">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-mono">
                    {typeof bus.currentLocation?.latitude === "number"
                      ? bus.currentLocation.latitude.toFixed(4)
                      : "0.0000"}
                    ,{" "}
                    {typeof bus.currentLocation?.longitude === "number"
                      ? bus.currentLocation.longitude.toFixed(4)
                      : "0.0000"}
                  </span>
                </div>
              </div>

              {bus.status === BUS_STATUS.ACTIVE && (
                <div className="flex items-center space-x-2 mt-4 p-2 bg-green-50 rounded-lg">
                  <Activity className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-green-700">
                    Live Tracking Active
                  </span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Add/Edit Bus Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-screen overflow-y-auto">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              {editingBus ? "Edit Bus" : "Add New Bus"}
            </h2>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Bus Number
                </label>
                <input
                  type="text"
                  name="busNumber"
                  value={formData.busNumber}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Capacity
                </label>
                <input
                  type="number"
                  name="capacity"
                  value={formData.capacity}
                  onChange={handleInputChange}
                  min="1"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {Object.entries(BUS_STATUS).map(([key, value]) => (
                    <option key={key} value={value}>
                      {value.replace("_", " ").toUpperCase()}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Assigned Driver
                </label>
                <select
                  name="driverId"
                  value={formData.driverId}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Driver</option>
                  {drivers.map((driver) => (
                    <option key={driver.id} value={driver.id}>
                      {driver.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Assigned Route
                </label>
                <select
                  name="routeId"
                  value={formData.routeId}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Route</option>
                  {routes.map((route) => (
                    <option key={route.id} value={route.id}>
                      {route.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Current Latitude
                  </label>
                  <input
                    type="number"
                    name="currentLocation.latitude"
                    value={formData.currentLocation.latitude}
                    onChange={handleInputChange}
                    step="any"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Current Longitude
                  </label>
                  <input
                    type="number"
                    name="currentLocation.longitude"
                    value={formData.currentLocation.longitude}
                    onChange={handleInputChange}
                    step="any"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700"
                >
                  {editingBus ? "Update Bus" : "Add Bus"}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowForm(false);
                    setEditingBus(null);
                    setFormData(DEFAULT_BUS);
                  }}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Bus-Route Assignment Component */}
      {showAssignments && (
        <div className="mt-8">
          <BusRouteAssignment
            bus={selectedBus}
            routes={routes}
            onAssignmentChange={handleAssignmentChange}
          />
        </div>
      )}
    </div>
  );
};

export default BusTracker;
