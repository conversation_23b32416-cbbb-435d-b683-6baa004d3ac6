import React, { useState, useEffect } from "react";
import { useRealTimeTransportData } from "../hooks/useRealTimeData";
import { BUS_STATUS, DRIVER_STATUS, ROUTE_STATUS } from "../utils/constants";
import {
  Bus,
  Users,
  MapPin,
  Route,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Zap,
  Shield,
  Fuel,
  Calendar,
  Navigation,
  UserCheck,
  AlertCircle,
  Settings,
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";

const Dashboard = () => {
  const { buses, drivers, stations, routes, loading } =
    useRealTimeTransportData();
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Enhanced statistics calculations
  const activeBuses = buses.filter(
    (bus) => bus.status === BUS_STATUS.ACTIVE
  ).length;
  const inactiveBuses = buses.filter(
    (bus) => bus.status === BUS_STATUS.INACTIVE
  ).length;
  const maintenanceBuses = buses.filter(
    (bus) => bus.status === BUS_STATUS.MAINTENANCE
  ).length;
  const outOfServiceBuses = buses.filter(
    (bus) => bus.status === BUS_STATUS.OUT_OF_SERVICE
  ).length;

  const availableDrivers = drivers.filter(
    (driver) => driver.status === DRIVER_STATUS.AVAILABLE
  ).length;
  const onDutyDrivers = drivers.filter(
    (driver) => driver.status === DRIVER_STATUS.ON_DUTY
  ).length;
  const offDutyDrivers = drivers.filter(
    (driver) => driver.status === DRIVER_STATUS.OFF_DUTY
  ).length;
  const driversOnBreak = drivers.filter(
    (driver) => driver.status === DRIVER_STATUS.BREAK
  ).length;

  const totalPassengers = stations.reduce(
    (sum, station) => sum + (station.currentPassengerCount || 0),
    0
  );
  const averagePassengersPerStation =
    stations.length > 0 ? Math.round(totalPassengers / stations.length) : 0;
  const highTrafficStations = stations.filter(
    (station) => (station.currentPassengerCount || 0) > 20
  ).length;

  const activeRoutes = routes.filter(
    (route) => route.status === ROUTE_STATUS.ACTIVE
  ).length;
  const inactiveRoutes = routes.filter(
    (route) => route.status === ROUTE_STATUS.INACTIVE
  ).length;
  const underConstructionRoutes = routes.filter(
    (route) => route.status === ROUTE_STATUS.UNDER_CONSTRUCTION
  ).length;

  // Assignment statistics
  const assignedBuses = buses.filter(
    (bus) => bus.driverId && bus.driverId !== ""
  ).length;
  const unassignedBuses = buses.length - assignedBuses;
  const busesWithRoutes = buses.filter(
    (bus) => bus.routeId && bus.routeId !== ""
  ).length;
  const busesWithoutRoutes = buses.length - busesWithRoutes;

  // Fleet efficiency
  const fleetUtilization =
    buses.length > 0 ? Math.round((activeBuses / buses.length) * 100) : 0;
  const driverUtilization =
    drivers.length > 0
      ? Math.round(((onDutyDrivers + availableDrivers) / drivers.length) * 100)
      : 0;
  const routeUtilization =
    routes.length > 0 ? Math.round((activeRoutes / routes.length) * 100) : 0;

  // Prepare chart data
  const stationData = stations.map((station) => ({
    name: station.name?.substring(0, 10) + "..." || "Unknown",
    passengers: station.currentPassengerCount || 0,
  }));

  const busStatusData = [
    {
      name: "Active",
      value: buses.filter((b) => b.status === BUS_STATUS.ACTIVE).length,
      color: "#10B981",
    },
    {
      name: "Inactive",
      value: buses.filter((b) => b.status === BUS_STATUS.INACTIVE).length,
      color: "#6B7280",
    },
    {
      name: "Maintenance",
      value: buses.filter((b) => b.status === BUS_STATUS.MAINTENANCE).length,
      color: "#F59E0B",
    },
    {
      name: "Out of Service",
      value: buses.filter((b) => b.status === BUS_STATUS.OUT_OF_SERVICE).length,
      color: "#EF4444",
    },
  ];

  const StatCard = ({
    title,
    value,
    icon: Icon,
    color,
    description,
    trend,
  }) => (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 p-6 hover:transform hover:scale-[1.02]">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">
              {title}
            </p>
            {trend && (
              <div
                className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                  trend > 0
                    ? "bg-green-100 text-green-700"
                    : "bg-red-100 text-red-700"
                }`}
              >
                <span>{trend > 0 ? "↗" : "↘"}</span>
                <span>{Math.abs(trend)}%</span>
              </div>
            )}
          </div>
          <p className={`text-3xl font-bold ${color} mb-1`}>{value}</p>
          {description && (
            <p className="text-sm text-gray-500">{description}</p>
          )}
        </div>
        <div
          className={`p-3 rounded-xl ${color
            .replace("text-", "bg-")
            .replace("600", "100")} shadow-md`}
        >
          <Icon className={`h-6 w-6 ${color}`} />
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-8">
      {/* Enhanced Header */}
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <div className="flex items-center space-x-4 mb-3">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-600">
                  Live Data
                </span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <Clock className="h-4 w-4" />
                <span>{currentTime.toLocaleTimeString()}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <Calendar className="h-4 w-4" />
                <span>{currentTime.toLocaleDateString()}</span>
              </div>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Transport Command Center
            </h1>
            <p className="text-gray-600 text-lg">
              Real-time monitoring and analytics for your transport network
            </p>
          </div>

          {/* Quick Stats Summary */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {fleetUtilization}%
              </div>
              <div className="text-xs text-gray-500">Fleet Utilization</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {driverUtilization}%
              </div>
              <div className="text-xs text-gray-500">Driver Utilization</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {routeUtilization}%
              </div>
              <div className="text-xs text-gray-500">Route Utilization</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {highTrafficStations}
              </div>
              <div className="text-xs text-gray-500">High Traffic</div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
        <StatCard
          title="Fleet Status"
          value={`${activeBuses}/${buses.length}`}
          icon={Bus}
          color="text-emerald-600"
          description={`${activeBuses} active, ${inactiveBuses} inactive, ${maintenanceBuses} maintenance`}
          trend={fleetUtilization > 70 ? 12 : -5}
        />
        <StatCard
          title="Driver Status"
          value={`${onDutyDrivers + availableDrivers}/${drivers.length}`}
          icon={UserCheck}
          color="text-blue-600"
          description={`${onDutyDrivers} on duty, ${availableDrivers} available, ${driversOnBreak} on break`}
          trend={driverUtilization > 80 ? 8 : -3}
        />
        <StatCard
          title="Passenger Traffic"
          value={totalPassengers}
          icon={Users}
          color="text-amber-600"
          description={`${averagePassengersPerStation} avg/station, ${highTrafficStations} high traffic`}
          trend={totalPassengers > 100 ? 15 : -7}
        />
        <StatCard
          title="Route Network"
          value={`${activeRoutes}/${routes.length}`}
          icon={Navigation}
          color="text-purple-600"
          description={`${activeRoutes} active, ${inactiveRoutes} inactive, ${underConstructionRoutes} under construction`}
          trend={routeUtilization > 75 ? 10 : -2}
        />
      </div>

      {/* Assignment Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide mb-2">
                Bus Assignments
              </p>
              <p className="text-3xl font-bold text-indigo-600 mb-1">
                {assignedBuses}/{buses.length}
              </p>
              <p className="text-sm text-gray-500">
                {unassignedBuses} buses need drivers
              </p>
            </div>
            <div className="p-3 bg-indigo-100 rounded-xl">
              <Shield className="h-6 w-6 text-indigo-600" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide mb-2">
                Route Assignments
              </p>
              <p className="text-3xl font-bold text-cyan-600 mb-1">
                {busesWithRoutes}/{buses.length}
              </p>
              <p className="text-sm text-gray-500">
                {busesWithoutRoutes} buses need routes
              </p>
            </div>
            <div className="p-3 bg-cyan-100 rounded-xl">
              <Route className="h-6 w-6 text-cyan-600" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide mb-2">
                System Efficiency
              </p>
              <p className="text-3xl font-bold text-green-600 mb-1">
                {Math.round(
                  (fleetUtilization + driverUtilization + routeUtilization) / 3
                )}
                %
              </p>
              <p className="text-sm text-gray-500">
                Overall system performance
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-xl">
              <Zap className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide mb-2">
                Alerts & Issues
              </p>
              <p className="text-3xl font-bold text-red-600 mb-1">
                {maintenanceBuses + outOfServiceBuses + highTrafficStations}
              </p>
              <p className="text-sm text-gray-500">Requires attention</p>
            </div>
            <div className="p-3 bg-red-100 rounded-xl">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Station Passenger Count Chart */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900">
                Passengers by Station
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Real-time passenger distribution
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-xl">
              <MapPin className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <ResponsiveContainer width="100%" height={320}>
            <BarChart
              data={stationData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis dataKey="name" tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip
                contentStyle={{
                  backgroundColor: "rgba(255, 255, 255, 0.95)",
                  border: "none",
                  borderRadius: "12px",
                  boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                }}
              />
              <Bar
                dataKey="passengers"
                fill="url(#blueGradient)"
                radius={[4, 4, 0, 0]}
              />
              <defs>
                <linearGradient id="blueGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#3B82F6" />
                  <stop offset="100%" stopColor="#1D4ED8" />
                </linearGradient>
              </defs>
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Bus Status Distribution */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900">
                Bus Status Distribution
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Fleet status overview
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-xl">
              <Bus className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <ResponsiveContainer width="100%" height={320}>
            <PieChart>
              <Pie
                data={busStatusData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value, percent }) =>
                  `${name}: ${value} (${(percent * 100).toFixed(0)}%)`
                }
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
                stroke="none"
              >
                {busStatusData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: "rgba(255, 255, 255, 0.95)",
                  border: "none",
                  borderRadius: "12px",
                  boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Operational Insights */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Real-time Alerts */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900">
                Real-time Alerts
              </h3>
              <p className="text-sm text-gray-600 mt-1">System notifications</p>
            </div>
            <div className="p-3 bg-red-100 rounded-xl">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
          </div>
          <div className="space-y-4">
            {maintenanceBuses > 0 && (
              <div className="flex items-center space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <Settings className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="font-medium text-yellow-800">
                    {maintenanceBuses} buses in maintenance
                  </p>
                  <p className="text-sm text-yellow-600">
                    Schedule maintenance completion
                  </p>
                </div>
              </div>
            )}
            {outOfServiceBuses > 0 && (
              <div className="flex items-center space-x-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <div>
                  <p className="font-medium text-red-800">
                    {outOfServiceBuses} buses out of service
                  </p>
                  <p className="text-sm text-red-600">
                    Immediate attention required
                  </p>
                </div>
              </div>
            )}
            {highTrafficStations > 0 && (
              <div className="flex items-center space-x-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <Users className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="font-medium text-orange-800">
                    {highTrafficStations} high traffic stations
                  </p>
                  <p className="text-sm text-orange-600">
                    Consider additional buses
                  </p>
                </div>
              </div>
            )}
            {unassignedBuses > 0 && (
              <div className="flex items-center space-x-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <Bus className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-800">
                    {unassignedBuses} buses need drivers
                  </p>
                  <p className="text-sm text-blue-600">
                    Assign available drivers
                  </p>
                </div>
              </div>
            )}
            {maintenanceBuses === 0 &&
              outOfServiceBuses === 0 &&
              highTrafficStations === 0 &&
              unassignedBuses === 0 && (
                <div className="flex items-center space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium text-green-800">
                      All systems operational
                    </p>
                    <p className="text-sm text-green-600">
                      No alerts at this time
                    </p>
                  </div>
                </div>
              )}
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900">
                Performance Metrics
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Key performance indicators
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-xl">
              <TrendingUp className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="space-y-6">
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Fleet Utilization
                </span>
                <span className="text-sm font-bold text-gray-900">
                  {fleetUtilization}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    fleetUtilization > 70
                      ? "bg-green-500"
                      : fleetUtilization > 50
                      ? "bg-yellow-500"
                      : "bg-red-500"
                  }`}
                  style={{ width: `${fleetUtilization}%` }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Driver Utilization
                </span>
                <span className="text-sm font-bold text-gray-900">
                  {driverUtilization}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    driverUtilization > 80
                      ? "bg-green-500"
                      : driverUtilization > 60
                      ? "bg-yellow-500"
                      : "bg-red-500"
                  }`}
                  style={{ width: `${driverUtilization}%` }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Route Coverage
                </span>
                <span className="text-sm font-bold text-gray-900">
                  {routeUtilization}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    routeUtilization > 75
                      ? "bg-green-500"
                      : routeUtilization > 50
                      ? "bg-yellow-500"
                      : "bg-red-500"
                  }`}
                  style={{ width: `${routeUtilization}%` }}
                ></div>
              </div>
            </div>
            <div className="pt-4 border-t border-gray-200">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {Math.round(
                    (fleetUtilization + driverUtilization + routeUtilization) /
                      3
                  )}
                  %
                </div>
                <div className="text-sm text-gray-600">
                  Overall System Health
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity / Alerts */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* High Traffic Stations */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900">
                High Traffic Stations
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Stations with most passengers
              </p>
            </div>
            <div className="p-3 bg-orange-100 rounded-xl">
              <AlertTriangle className="h-6 w-6 text-orange-600" />
            </div>
          </div>
          <div className="space-y-4">
            {stations
              .sort(
                (a, b) =>
                  (b.currentPassengerCount || 0) -
                  (a.currentPassengerCount || 0)
              )
              .slice(0, 5)
              .map((station, index) => (
                <div
                  key={station.id}
                  className="group flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl hover:from-blue-50 hover:to-purple-50 transition-all duration-200 border border-gray-200 hover:border-blue-200"
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full text-sm font-bold text-blue-600">
                      {index + 1}
                    </div>
                    <div className="flex items-center space-x-3">
                      <MapPin className="h-5 w-5 text-gray-500 group-hover:text-blue-500 transition-colors" />
                      <span className="font-semibold text-gray-900">
                        {station.name || "Unknown Station"}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="text-right">
                      <span className="text-lg font-bold text-gray-900">
                        {station.currentPassengerCount || 0}
                      </span>
                      <p className="text-xs text-gray-500">passengers</p>
                    </div>
                    {(station.currentPassengerCount || 0) > 20 && (
                      <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
                    )}
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* System Status */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900">System Status</h3>
              <p className="text-sm text-gray-600 mt-1">
                Real-time system monitoring
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-xl">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200">
              <div className="flex items-center space-x-4">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="font-semibold text-gray-900">
                    Real-time Tracking
                  </span>
                </div>
              </div>
              <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                Active
              </span>
            </div>
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200">
              <div className="flex items-center space-x-4">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="font-semibold text-gray-900">
                    Database Connection
                  </span>
                </div>
              </div>
              <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                Connected
              </span>
            </div>
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
              <div className="flex items-center space-x-4">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-blue-500" />
                  <span className="font-semibold text-gray-900">
                    Last Update
                  </span>
                </div>
              </div>
              <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                Just now
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
