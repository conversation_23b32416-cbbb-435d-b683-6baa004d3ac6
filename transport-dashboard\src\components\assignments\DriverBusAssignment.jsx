import React, { useState, useEffect } from "react";
import {
  assignDriverToBus,
  unassignDriverFromBus,
  getAvailableDrivers,
  getAvailableBuses,
} from "../../firebase/services";
import { Bus, User, Link, Unlink, CheckCircle, XCircle } from "lucide-react";

const DriverBusAssignment = ({ driver, bus, onAssignmentChange }) => {
  const [availableDrivers, setAvailableDrivers] = useState([]);
  const [availableBuses, setAvailableBuses] = useState([]);
  const [selectedDriver, setSelectedDriver] = useState("");
  const [selectedBus, setSelectedBus] = useState("");
  const [loading, setLoading] = useState(false);
  const [showAssignForm, setShowAssignForm] = useState(false);

  useEffect(() => {
    loadAvailableOptions();
  }, []);

  const loadAvailableOptions = async () => {
    try {
      const [drivers, buses] = await Promise.all([
        getAvailableDrivers(),
        getAvailableBuses(),
      ]);
      setAvailableDrivers(drivers);
      setAvailableBuses(buses);
    } catch (error) {
      console.error("Error loading available options:", error);
    }
  };

  const handleAssign = async () => {
    if (!selectedDriver || !selectedBus) return;

    setLoading(true);
    try {
      await assignDriverToBus(selectedDriver, selectedBus);
      setShowAssignForm(false);
      setSelectedDriver("");
      setSelectedBus("");
      await loadAvailableOptions();
      if (onAssignmentChange) onAssignmentChange();
    } catch (error) {
      console.error("Error assigning driver to bus:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleUnassign = async (driverId, busId) => {
    if (
      !window.confirm(
        "Are you sure you want to unassign this driver from the bus?"
      )
    ) {
      return;
    }

    setLoading(true);
    try {
      await unassignDriverFromBus(driverId, busId);
      await loadAvailableOptions();
      if (onAssignmentChange) onAssignmentChange();
    } catch (error) {
      console.error("Error unassigning driver from bus:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Link className="h-5 w-5 mr-2 text-blue-600" />
          Driver-Bus Assignments
        </h3>
        <button onClick={() => setShowAssignForm(true)} className="btn-primary">
          New Assignment
        </button>
      </div>

      {/* Current Assignment Display */}
      {driver && bus && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-green-600" />
                <span className="font-medium">{driver.name}</span>
              </div>
              <div className="text-green-600">
                <Link className="h-4 w-4" />
              </div>
              <div className="flex items-center space-x-2">
                <Bus className="h-4 w-4 text-green-600" />
                <span className="font-medium">{bus.busNumber}</span>
              </div>
            </div>
            <button
              onClick={() => handleUnassign(driver.id, bus.id)}
              disabled={loading}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
            >
              <Unlink className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Assignment Form */}
      {showAssignForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              Assign Driver to Bus
            </h4>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Driver
                </label>
                <select
                  value={selectedDriver}
                  onChange={(e) => setSelectedDriver(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Choose a driver...</option>
                  {availableDrivers.map((driver) => (
                    <option key={driver.id} value={driver.id}>
                      {driver.name} - {driver.licenseNumber}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Bus
                </label>
                <select
                  value={selectedBus}
                  onChange={(e) => setSelectedBus(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Choose a bus...</option>
                  {availableBuses.map((bus) => (
                    <option key={bus.id} value={bus.id}>
                      {bus.busNumber} - Capacity: {bus.capacity}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleAssign}
                disabled={loading || !selectedDriver || !selectedBus}
                className="flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Assigning...
                  </div>
                ) : (
                  "Assign"
                )}
              </button>
              <button
                onClick={() => {
                  setShowAssignForm(false);
                  setSelectedDriver("");
                  setSelectedBus("");
                }}
                className="flex-1 btn-secondary"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Available Options Summary */}
      <div className="grid grid-cols-2 gap-4">
        <div className="p-4 bg-blue-50 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-blue-700">
              Available Drivers
            </span>
            <span className="text-lg font-bold text-blue-600">
              {availableDrivers.length}
            </span>
          </div>
        </div>
        <div className="p-4 bg-green-50 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-green-700">
              Available Buses
            </span>
            <span className="text-lg font-bold text-green-600">
              {availableBuses.length}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DriverBusAssignment;
