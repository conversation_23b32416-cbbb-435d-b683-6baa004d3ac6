// Bus status constants
export const BUS_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  MAINTENANCE: 'maintenance',
  OUT_OF_SERVICE: 'out_of_service'
};

// Driver status constants
export const DRIVER_STATUS = {
  AVAILABLE: 'available',
  ON_DUTY: 'on_duty',
  OFF_DUTY: 'off_duty',
  BREAK: 'break'
};

// Route status constants
export const ROUTE_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  UNDER_CONSTRUCTION: 'under_construction'
};

// Station types
export const STATION_TYPES = {
  BUS_STOP: 'bus_stop',
  TERMINAL: 'terminal',
  INTERCHANGE: 'interchange'
};

// User roles
export const USER_ROLES = {
  ADMIN: 'admin',
  OPERATOR: 'operator',
  DRIVER: 'driver'
};

// Assignment status
export const ASSIGNMENT_STATUS = {
  ASSIGNED: 'assigned',
  UNASSIGNED: 'unassigned',
  PENDING: 'pending'
};

// Default values for new entities
export const DEFAULT_BUS = {
  busNumber: '',
  capacity: 50,
  currentLocation: {
    latitude: 0,
    longitude: 0
  },
  status: BUS_STATUS.INACTIVE,
  routeId: '',
  driverId: '',
  lastUpdated: null
};

export const DEFAULT_DRIVER = {
  name: '',
  licenseNumber: '',
  phoneNumber: '',
  email: '',
  status: DRIVER_STATUS.OFF_DUTY,
  currentLocation: {
    latitude: 0,
    longitude: 0
  },
  assignedBusId: '',
  assignedRouteId: ''
};

export const DEFAULT_STATION = {
  name: '',
  location: {
    latitude: 0,
    longitude: 0
  },
  type: STATION_TYPES.BUS_STOP,
  currentPassengerCount: 0,
  maxCapacity: 100,
  amenities: []
};

export const DEFAULT_ROUTE = {
  name: '',
  description: '',
  stations: [], // Array of station IDs in order
  status: ROUTE_STATUS.INACTIVE,
  estimatedDuration: 0, // in minutes
  frequency: 15 // in minutes
};

// Chart colors for visualizations
export const CHART_COLORS = {
  PRIMARY: '#3B82F6',
  SECONDARY: '#10B981',
  WARNING: '#F59E0B',
  DANGER: '#EF4444',
  INFO: '#6366F1',
  SUCCESS: '#059669'
};

// Navigation menu items
export const MENU_ITEMS = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'LayoutDashboard',
    path: '/'
  },
  {
    id: 'buses',
    label: 'Buses',
    icon: 'Bus',
    path: '/buses'
  },
  {
    id: 'drivers',
    label: 'Drivers',
    icon: 'Users',
    path: '/drivers'
  },
  {
    id: 'stations',
    label: 'Stations',
    icon: 'MapPin',
    path: '/stations'
  },
  {
    id: 'routes',
    label: 'Routes',
    icon: 'Route',
    path: '/routes'
  },
  {
    id: 'setup',
    label: 'Setup',
    icon: 'Settings',
    path: '/setup'
  },
  {
    id: 'test',
    label: 'CRUD Test',
    icon: 'TestTube',
    path: '/test'
  },
  {
    id: 'assignment-test',
    label: 'Assignment Test',
    icon: 'TestTube',
    path: '/assignment-test'
  }
];

// Status color mappings
export const STATUS_COLORS = {
  [BUS_STATUS.ACTIVE]: 'text-green-600 bg-green-100',
  [BUS_STATUS.INACTIVE]: 'text-gray-600 bg-gray-100',
  [BUS_STATUS.MAINTENANCE]: 'text-yellow-600 bg-yellow-100',
  [BUS_STATUS.OUT_OF_SERVICE]: 'text-red-600 bg-red-100',
  
  [DRIVER_STATUS.AVAILABLE]: 'text-green-600 bg-green-100',
  [DRIVER_STATUS.ON_DUTY]: 'text-blue-600 bg-blue-100',
  [DRIVER_STATUS.OFF_DUTY]: 'text-gray-600 bg-gray-100',
  [DRIVER_STATUS.BREAK]: 'text-yellow-600 bg-yellow-100',
  
  [ROUTE_STATUS.ACTIVE]: 'text-green-600 bg-green-100',
  [ROUTE_STATUS.INACTIVE]: 'text-gray-600 bg-gray-100',
  [ROUTE_STATUS.UNDER_CONSTRUCTION]: 'text-orange-600 bg-orange-100'
};
