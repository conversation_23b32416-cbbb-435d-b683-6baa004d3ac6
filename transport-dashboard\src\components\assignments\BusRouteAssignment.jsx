import React, { useState } from 'react';
import { assignBusToRoute, unassignBusFromRoute } from '../../firebase/services';
import { Bus, Route, Link, Unlink, MapPin } from 'lucide-react';

const BusRouteAssignment = ({ bus, routes, onAssignmentChange }) => {
  const [selectedRoute, setSelectedRoute] = useState(bus?.routeId || '');
  const [loading, setLoading] = useState(false);
  const [showAssignForm, setShowAssignForm] = useState(false);

  const currentRoute = routes.find(route => route.id === bus?.routeId);
  const availableRoutes = routes.filter(route => route.status === 'active');

  const handleAssign = async () => {
    if (!selectedRoute || !bus) return;
    
    setLoading(true);
    try {
      await assignBusToRoute(bus.id, selectedRoute);
      setShowAssignForm(false);
      if (onAssignmentChange) onAssignmentChange();
    } catch (error) {
      console.error('Error assigning bus to route:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUnassign = async () => {
    if (!bus?.routeId) return;
    
    if (!window.confirm('Are you sure you want to unassign this bus from its route?')) {
      return;
    }
    
    setLoading(true);
    try {
      await unassignBusFromRoute(bus.id);
      setSelectedRoute('');
      if (onAssignmentChange) onAssignmentChange();
    } catch (error) {
      console.error('Error unassigning bus from route:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!bus) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <p className="text-gray-500 text-center">Select a bus to manage route assignments</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Link className="h-5 w-5 mr-2 text-purple-600" />
          Bus-Route Assignment
        </h3>
        <div className="flex items-center space-x-2">
          <Bus className="h-4 w-4 text-gray-400" />
          <span className="font-medium">{bus.busNumber}</span>
        </div>
      </div>

      {/* Current Assignment */}
      {currentRoute ? (
        <div className="mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Route className="h-4 w-4 text-purple-600" />
                <span className="font-medium">{currentRoute.name}</span>
              </div>
              <div className="text-sm text-gray-600">
                {currentRoute.stations?.length || 0} stations
              </div>
            </div>
            <button
              onClick={handleUnassign}
              disabled={loading}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
              title="Unassign from route"
            >
              <Unlink className="h-4 w-4" />
            </button>
          </div>
          
          {/* Route Details */}
          <div className="mt-3 text-sm text-gray-600">
            <p>{currentRoute.description}</p>
            <div className="flex items-center mt-2 space-x-4">
              <span>Duration: {currentRoute.estimatedDuration || 0} min</span>
              <span>Frequency: {currentRoute.frequency || 15} min</span>
            </div>
          </div>
        </div>
      ) : (
        <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="flex items-center justify-center text-gray-500">
            <MapPin className="h-4 w-4 mr-2" />
            <span>No route assigned</span>
          </div>
        </div>
      )}

      {/* Assignment Controls */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Assign to Route
          </label>
          <div className="flex space-x-2">
            <select
              value={selectedRoute}
              onChange={(e) => setSelectedRoute(e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="">Select a route...</option>
              {availableRoutes.map((route) => (
                <option key={route.id} value={route.id}>
                  {route.name} ({route.stations?.length || 0} stations)
                </option>
              ))}
            </select>
            <button
              onClick={handleAssign}
              disabled={loading || !selectedRoute || selectedRoute === bus.routeId}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Assigning...
                </div>
              ) : (
                'Assign'
              )}
            </button>
          </div>
        </div>

        {/* Route Preview */}
        {selectedRoute && selectedRoute !== bus.routeId && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            {(() => {
              const route = routes.find(r => r.id === selectedRoute);
              return route ? (
                <div>
                  <h4 className="font-medium text-blue-900 mb-2">{route.name}</h4>
                  <p className="text-sm text-blue-700 mb-2">{route.description}</p>
                  <div className="flex items-center space-x-4 text-sm text-blue-600">
                    <span>{route.stations?.length || 0} stations</span>
                    <span>{route.estimatedDuration || 0} min duration</span>
                    <span>{route.frequency || 15} min frequency</span>
                  </div>
                </div>
              ) : null;
            })()}
          </div>
        )}
      </div>

      {/* Statistics */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {availableRoutes.length}
            </div>
            <div className="text-sm text-gray-600">Available Routes</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {currentRoute ? '1' : '0'}
            </div>
            <div className="text-sm text-gray-600">Assigned Routes</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusRouteAssignment;
